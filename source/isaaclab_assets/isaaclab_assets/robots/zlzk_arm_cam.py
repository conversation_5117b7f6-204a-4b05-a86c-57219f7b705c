# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Configuration for the ZLZK Emika robots.

The following configurations are available:

* :obj:`ZLZK_PANDA_CFG`: ZLZK Emika Panda robot with Panda hand
* :obj:`ZLZK_PANDA_HIGH_PD_CFG`: ZLZK Emika Panda robot with Panda hand with stiffer PD control

Reference: https://github.com/frankaemika/franka_ros
"""

import isaaclab.sim as sim_utils
from isaaclab.actuators import ImplicitActuatorCfg
from isaaclab.assets.articulation import ArticulationCfg
# from isaaclab.utils.assets import ISAACLAB_NUCLEUS_DIR

##
# Configuration
##

ZLZK_PANDA_CAM_CFG = ArticulationCfg(
    spawn=sim_utils.UsdFileCfg(
        # usd_path=f"{ISAACLAB_NUCLEUS_DIR}/Robots/FrankaEmika/panda_instanceable.usd",
        usd_path="/home/<USER>/IsaacLab/usd_files/Test-Arm-with-cam.usd",
        activate_contact_sensors=False,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            max_depenetration_velocity=5.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=True, 
            solver_position_iteration_count=8,
            solver_velocity_iteration_count=0
        ),
        # collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.005, rest_offset=0.0),
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0, 0, 0),
        rot=(0.707, 0, 0.707, 0),
        joint_pos={
            "leftarm_link12leftarm_baselink": 0.0,
            "leftarm_link22leftarm_link1": 0.0,
            "leftarm_link32leftarm_link2": 0.0,
            "leftarm_link42leftarm_link3": 1.97,
            "leftarm_link52leftarm_link4": 0.0,
            "leftarm_link62leftarm_link5": 1.17,
            "leftarm_link72leftarm_link6": 0.5,
            "leftgripper_leftlink1_joint" : 0.0,
            # "leftgripper_leftlink2_joint" : 0.0,
            "leftgripper_leftlink3_1_joint" : 0.0,
            "leftgripper_leftlink3_2_joint" : 0.0,
            "leftgripper_rightlink1_joint" : 0.0,
            # "leftgripper_rightlink2_joint" : 0.0,
            "leftgripper_rightlink3_1_joint" : 0.0,
            "leftgripper_rightlink3_2_joint" : 0.0
        },
    ),
    actuators={
        "ruirman_shoulder": ImplicitActuatorCfg(
            joint_names_expr=["leftarm_link12leftarm_baselink",
                              "leftarm_link22leftarm_link1"],
            effort_limit_sim=180.0,
            velocity_limit_sim=3.141,
            stiffness=200.0,
            damping=40.0,
        ),
        "ruirman_upperarm" : ImplicitActuatorCfg(
            joint_names_expr=["leftarm_link32leftarm_link2",
                              "leftarm_link42leftarm_link3"],
            effort_limit_sim=89.79,
            velocity_limit_sim=3.927,
            stiffness=200.0,
            damping=40.0,
        ),
        "ruirman_forearm": ImplicitActuatorCfg(
            joint_names_expr=["leftarm_link52leftarm_link4",
                              "leftarm_link62leftarm_link5",
                              "leftarm_link72leftarm_link6"],
            effort_limit_sim=30,
            velocity_limit_sim=3.927,
            stiffness=200.0,
            damping=40.0,
        ),
        "ruirman_hand": ImplicitActuatorCfg(
            joint_names_expr=["leftgripper_leftlink1_joint",
                              "leftgripper_leftlink3_1_joint",
                              "leftgripper_leftlink3_2_joint",
                              "leftgripper_rightlink1_joint",
                              "leftgripper_rightlink3_1_joint",
                              "leftgripper_rightlink3_2_joint"],
            effort_limit_sim={"leftgripper_leftlink1_joint" : 100.0,
                              "leftgripper_leftlink3_1_joint" : 0.0,
                              "leftgripper_leftlink3_2_joint" : 0.0,
                              "leftgripper_rightlink1_joint" : 100.0,
                              "leftgripper_rightlink3_1_joint" : 0.0,
                              "leftgripper_rightlink3_2_joint" : 0.0},
            velocity_limit_sim=3.141,
            stiffness=100,
            damping=20,
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)
"""Configuration of ZLZK Emika Panda robot."""


ZLZK_PANDA_CAM_HIGH_PD_CFG = ZLZK_PANDA_CAM_CFG.copy()
ZLZK_PANDA_CAM_HIGH_PD_CFG.spawn.rigid_props.disable_gravity = True
ZLZK_PANDA_CAM_HIGH_PD_CFG.actuators["ruirman_shoulder"].stiffness = 1000.0
ZLZK_PANDA_CAM_HIGH_PD_CFG.actuators["ruirman_shoulder"].damping = 200.0
ZLZK_PANDA_CAM_HIGH_PD_CFG.actuators["ruirman_upperarm"].stiffness = 1000.0
ZLZK_PANDA_CAM_HIGH_PD_CFG.actuators["ruirman_upperarm"].damping = 200.0
ZLZK_PANDA_CAM_HIGH_PD_CFG.actuators["ruirman_forearm"].stiffness = 500.0
ZLZK_PANDA_CAM_HIGH_PD_CFG.actuators["ruirman_forearm"].damping = 100.0
"""Configuration of ZLZK Emika Panda robot with stiffer PD control.

This configuration is useful for task-space control using differential IK.
"""
