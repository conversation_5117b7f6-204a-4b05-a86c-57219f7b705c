# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
改进的PPO配置 - 解决训练后期探索不足问题

主要改进：
1. 调整初始噪声标准差和衰减策略
2. 增加熵系数维持探索
3. 调整KL散度约束
4. 优化学习率调度
"""

from isaaclab_rl.rsl_rl.rl_cfg import RslRlOnPolicyRunnerCfg, RslRlPpoActorCriticCfg, RslRlPpoAlgorithmCfg
from isaaclab.utils import configclass


@configclass
class ImprovedLiftPPORunnerCfg(RslRlOnPolicyRunnerCfg):
    """改进的PPO配置 - 维持训练后期的探索能力"""
    
    num_steps_per_env = 24
    max_iterations = 2000  # 增加训练迭代数
    save_interval = 50
    experiment_name = "lift_improved_exploration"
    empirical_normalization = False
    
    # 改进1：调整策略网络配置
    policy = RslRlPpoActorCriticCfg(
        init_noise_std=1.2,  # 增加初始噪声标准差
        actor_hidden_dims=[256, 128, 64],
        critic_hidden_dims=[256, 128, 64],
        activation="elu",
        noise_std_type="scalar",  # 使用标量噪声类型
    )
    
    # 改进2：调整PPO算法参数
    algorithm = RslRlPpoAlgorithmCfg(
        value_loss_coef=1.0,
        use_clipped_value_loss=True,
        clip_param=0.2,
        
        # 关键改进：增加熵系数维持探索
        entropy_coef=0.01,  # 从0.006增加到0.01，维持更多探索
        
        num_learning_epochs=5,
        num_mini_batches=4,
        learning_rate=1.0e-4,
        
        # 改进3：使用固定学习率而非自适应，避免过早收敛
        schedule="fixed",  # 改为固定学习率
        
        gamma=0.98,
        lam=0.95,
        
        # 改进4：放宽KL散度约束，允许更多策略变化
        desired_kl=0.02,  # 从0.01增加到0.02
        
        max_grad_norm=1.0,
    )


@configclass 
class AdaptiveExplorationPPORunnerCfg(RslRlOnPolicyRunnerCfg):
    """自适应探索PPO配置 - 根据训练阶段动态调整探索"""
    
    num_steps_per_env = 24
    max_iterations = 2000
    save_interval = 50
    experiment_name = "lift_adaptive_exploration"
    empirical_normalization = False
    
    policy = RslRlPpoActorCriticCfg(
        init_noise_std=1.5,  # 更高的初始噪声
        actor_hidden_dims=[256, 128, 64],
        critic_hidden_dims=[256, 128, 64],
        activation="elu",
    )
    
    algorithm = RslRlPpoAlgorithmCfg(
        value_loss_coef=1.0,
        use_clipped_value_loss=True,
        clip_param=0.2,
        
        # 策略1：使用较高的熵系数
        entropy_coef=0.015,  # 更高的熵系数
        
        num_learning_epochs=5,
        num_mini_batches=4,
        learning_rate=1.0e-4,
        schedule="adaptive",  # 保持自适应但配合更宽松的KL约束
        gamma=0.98,
        lam=0.95,
        
        # 策略2：更宽松的KL约束
        desired_kl=0.025,  # 进一步放宽KL约束
        
        max_grad_norm=1.0,
    )


@configclass
class CurriculumExplorationPPORunnerCfg(RslRlOnPolicyRunnerCfg):
    """课程化探索PPO配置 - 分阶段调整探索策略"""
    
    num_steps_per_env = 24
    max_iterations = 2000
    save_interval = 50
    experiment_name = "lift_curriculum_exploration"
    empirical_normalization = False
    
    policy = RslRlPpoActorCriticCfg(
        init_noise_std=1.0,  # 标准初始噪声
        actor_hidden_dims=[256, 128, 64],
        critic_hidden_dims=[256, 128, 64],
        activation="elu",
    )
    
    algorithm = RslRlPpoAlgorithmCfg(
        value_loss_coef=1.0,
        use_clipped_value_loss=True,
        clip_param=0.2,
        
        # 策略：中等熵系数，配合环境中的探索奖励
        entropy_coef=0.008,
        
        num_learning_epochs=5,
        num_mini_batches=4,
        learning_rate=1.0e-4,
        schedule="adaptive",
        gamma=0.98,
        lam=0.95,
        desired_kl=0.015,  # 适中的KL约束
        max_grad_norm=1.0,
    )


# 使用说明和建议
"""
使用建议：

1. ImprovedLiftPPORunnerCfg - 基础改进版本
   - 适用于大多数情况
   - 通过增加熵系数和放宽KL约束维持探索
   - 使用固定学习率避免过早收敛

2. AdaptiveExplorationPPORunnerCfg - 高探索版本  
   - 适用于复杂任务或需要更多探索的场景
   - 更高的初始噪声和熵系数
   - 更宽松的KL约束

3. CurriculumExplorationPPORunnerCfg - 平衡版本
   - 配合改进的环境配置使用
   - 通过环境奖励而非仅算法参数维持探索
   - 更平衡的探索与收敛

训练监控建议：
- 监控策略熵值，确保不会过快衰减到0
- 观察动作标准差的变化趋势
- 跟踪KL散度，确保在合理范围内
- 监控探索相关的奖励项

如果仍然出现探索不足：
1. 进一步增加entropy_coef (可到0.02-0.03)
2. 增加desired_kl到0.03-0.05
3. 考虑使用噪声注入或好奇心驱动的方法
4. 调整课程学习的时间节点，延迟动作惩罚的激活
"""
